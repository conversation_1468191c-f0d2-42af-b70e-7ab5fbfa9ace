import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/controllers/theme_provider.dart';
import '/controllers/data_controller.dart';
import 'daily_quiz_screen.dart';
import 'leaderboard_screen.dart';
import 'how_to_use_screen.dart';
import 'about_us_screen.dart';
import 'contact_us_screen.dart';
import 'terms_conditions_screen.dart';
import 'category_screen_2.dart';

class SideNav extends StatelessWidget {
  const SideNav({super.key});

  @override
  Widget build(BuildContext context) {
    final ThemeController themeController = Get.find();
    final DataController dataController = Get.find<DataController>();

    return Drawer(
      child: Material(
        color: Color.fromRGBO(54, 102, 245, 0.858),
        child: ListView(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white,
                    child: Obx(() => Text(
                      dataController.userName.value.isNotEmpty
                        ? dataController.userName.value[0].toUpperCase()
                        : 'U',
                      style: TextStyle(
                        color: Color.fromRGBO(54, 102, 245, 0.858),
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    )),
                  ),
                  SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Obx(() => Text(
                          dataController.userName.value.isNotEmpty
                            ? dataController.userName.value
                            : "Guest User",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        )),
                        SizedBox(height: 8),
                        Obx(() => Text(
                          "${dataController.coins.value} coins",
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        )),
                        SizedBox(height: 4),
                        Obx(() => Text(
                          "Level ${dataController.getUserLevel()}",
                          style: TextStyle(
                            color: Colors.amber,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        )),
                      ],
                    ),
                  )
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Column(
                children: [
                  Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Rank: #${dataController.rank.value}",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        "Score: ${dataController.totalScore.value}",
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  )),
                  SizedBox(height: 8),
                  Obx(() => LinearProgressIndicator(
                    value: dataController.getLevelProgress(),
                    backgroundColor: Colors.white30,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
                  )),
                  SizedBox(height: 4),
                  Text(
                    "Level Progress",
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 48),
            listItem(
              label: "HOME",
              icon: Icons.home,
              onTap: () {
                Navigator.of(context).pop();
                Get.offAll(() => CategoryScreen());
              },
            ),
            listItem(
              label: "DAILY QUIZ",
              icon: Icons.quiz,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const DailyQuizScreen());
              },
            ),
            listItem(
              label: "Leaderboard",
              icon: Icons.leaderboard,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const LeaderboardScreen());
              },
            ),
            listItem(
              label: "How To Use",
              icon: Icons.question_answer,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const HowToUseScreen());
              },
            ),
            listItem(
              label: "About Us",
              icon: Icons.face,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const AboutUsScreen());
              },
            ),
            listItem(
              label: "Contact Us",
              icon: Icons.contact_page,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const ContactUsScreen());
              },
            ),
            listItem(
              label: "Terms & Conditions",
              icon: Icons.rule,
              onTap: () {
                Navigator.of(context).pop();
                Get.to(() => const TermsConditionsScreen());
              },
            ),
            Divider(color: Colors.white),
            ListTile(
              leading: Icon(Icons.palette, color: Colors.white),
              title: Text(
                'Toggle Theme',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () {
                themeController.toggleTheme();
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget listItem({
    required String label,
    required IconData icon,
    required Function() onTap,
  }) {
    final color = Colors.white;
    final hovercolor = Colors.white60;

    return ListTile(
      leading: Icon(icon, color: color),
      hoverColor: hovercolor,
      title: Text(label, style: TextStyle(color: color)),
      onTap: onTap,
    );
  }
}
